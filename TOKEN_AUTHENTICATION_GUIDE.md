# QJ Robots OpenAI API - Token Authentication Guide

## 🎉 Token Authentication System

我已经成功实现了完整的OpenAI兼容的token认证系统！现在你的API完全符合OpenAI的标准，支持Bearer token认证。

## 📋 生成的10个Token

以下是为你生成的10个OpenAI格式的访问token：

```
Token  1: sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc
Token  2: sk-6ezMcl5WFJB2dgn7Pi5SGKpPjvobFRvITsCPHTjQgof7y5qT
Token  3: sk-e1SwStgvODZPqmidGHeqxbntugsuP8L6ArtMzTWAaMOITclR
Token  4: sk-M5QXhnNNHe6v3VWCInscPt6zg0rcLSPfQ2da7W1fwYVjk8Ii
Token  5: sk-Za8sIvZARZvcBOdGqLOJ62Fjt4cK3QXihLRFRInfE9c5T2v3
Token  6: sk-qQtn93TiKBloDJ5YBIhWQpxd5IEoWuY4CnO4Cn7jcIHvmnLn
Token  7: sk-dDRZ49ATO4bpnipD6XUL27tI1jUw4C6mi2YMuFjt02DeNz80
Token  8: sk-CbdDQv4lXgqADN6x4qENxcs64MNgu4qozhvO3Gz9KLoJIgLG
Token  9: sk-Hc0GPO0ytG7NKOmI4GJ1CPmU8dMgNkOrGLGZNJ58qjn48gze
Token 10: sk-29CifBLIXSPbDB53JK3MCqniraRbEQAfJ7lef3Ipbct0ecof
```

## 🔧 配置步骤

### 1. 编辑Token映射配置

编辑 `token_mapping.json` 文件，将每个token对应的占位符替换为你的实际凭证：

```json
{
  "sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc": {
    "app_id": "你的实际APP_ID_1",
    "app_secret": "你的实际APP_SECRET_1",
    "description": "生产环境token",
    "created_at": "2025-08-02T15:17:34.513936",
    "last_used": "",
    "usage_count": 0
  },
  "sk-6ezMcl5WFJB2dgn7Pi5SGKpPjvobFRvITsCPHTjQgof7y5qT": {
    "app_id": "你的实际APP_ID_2", 
    "app_secret": "你的实际APP_SECRET_2",
    "description": "开发环境token",
    "created_at": "2025-08-02T15:17:34.514013",
    "last_used": "",
    "usage_count": 0
  }
  // ... 其他8个token
}
```

### 2. 使用CLI工具管理Token

我提供了一个完整的CLI工具来管理tokens：

```bash
# 查看所有tokens
python manage_tokens.py list

# 添加新token
python manage_tokens.py add YOUR_APP_ID YOUR_APP_SECRET --description "新的token"

# 更新现有token的凭证
python manage_tokens.py update sk-your-token --app-id NEW_APP_ID --app-secret NEW_SECRET

# 验证token
python manage_tokens.py validate sk-your-token

# 删除token
python manage_tokens.py remove sk-your-token

# 查看使用示例
python manage_tokens.py examples
```

## 🚀 使用方法

### 1. 启动服务器

```bash
python openai_api_server.py
```

服务器将在 `http://localhost:8000` 启动，支持以下端点：

- `POST /v1/chat/completions` - OpenAI兼容的聊天完成
- `GET /v1/models` - 列出可用模型
- `GET /v1/functions` - 列出感知功能
- `GET /admin/tokens` - 管理tokens（管理端点）

### 2. 使用OpenAI客户端库

```python
import openai
import json

# 配置客户端
openai.api_base = "http://localhost:8000/v1"
openai.api_key = "sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc"

# 发起请求
response = openai.ChatCompletion.create(
    model="qj-perception-v1",
    messages=[{
        "role": "user",
        "content": json.dumps({
            "function": "check_image",
            "arguments": {
                "image_type": "2D",
                "image_url": "https://example.com/image.jpg",
                "object_names": ["apple", "banana"]
            }
        })
    }]
)

print(response.choices[0].message.content)
```

### 3. 使用HTTP请求

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "qj-perception-v1",
    "messages": [{
      "role": "user",
      "content": "{\"function\": \"check_image\", \"arguments\": {\"image_type\": \"2D\", \"image_url\": \"https://example.com/image.jpg\", \"object_names\": [\"apple\"]}}"
    }]
  }'
```

### 4. 使用Python requests

```python
import requests
import json

headers = {
    "Authorization": "Bearer sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc",
    "Content-Type": "application/json"
}

data = {
    "model": "qj-perception-v1",
    "messages": [{
        "role": "user",
        "content": json.dumps({
            "function": "split_image",
            "arguments": {
                "image_type": "2D",
                "image_url": "https://example.com/image.jpg",
                "object_names": ["cup", "plate"]
            }
        })
    }]
}

response = requests.post(
    "http://localhost:8000/v1/chat/completions",
    headers=headers,
    json=data
)

result = response.json()
print(result["choices"][0]["message"]["content"])
```

## 🔒 安全特性

1. **Bearer Token认证**: 完全符合OpenAI标准
2. **Token映射**: 每个token对应独立的APP_ID/APP_SECRET
3. **使用统计**: 自动跟踪token使用次数和最后使用时间
4. **Token管理**: 完整的CRUD操作支持
5. **错误处理**: 标准的HTTP状态码和错误消息

## 📊 管理端点

服务器提供了管理端点（生产环境中应该加上认证）：

```bash
# 查看所有tokens
curl http://localhost:8000/admin/tokens

# 创建新token
curl -X POST "http://localhost:8000/admin/tokens?app_id=YOUR_APP_ID&app_secret=YOUR_SECRET&description=New+token"

# 验证token
curl http://localhost:8000/admin/tokens/sk-your-token/validate

# 删除token
curl -X DELETE http://localhost:8000/admin/tokens/sk-your-token
```

## 🧪 测试

运行完整的测试套件：

```bash
# 测试token认证系统
python test_token_auth.py

# 测试OpenAI API兼容性
python test_openai_api.py
```

## 📝 配置文件结构

`token_mapping.json` 文件结构：

```json
{
  "sk-token-here": {
    "app_id": "实际的QJ_APP_ID",
    "app_secret": "实际的QJ_APP_SECRET", 
    "description": "token描述",
    "created_at": "创建时间",
    "last_used": "最后使用时间",
    "usage_count": 使用次数
  }
}
```

## 🎯 下一步

1. **配置凭证**: 编辑 `token_mapping.json`，填入你的实际APP_ID和APP_SECRET
2. **启动服务器**: `python openai_api_server.py`
3. **测试连接**: 使用任意一个token测试API调用
4. **集成应用**: 在你的应用中使用这些tokens

## 🔄 从旧版本迁移

如果你之前直接使用环境变量，现在可以：

1. 创建一个token映射到你现有的凭证
2. 更新你的客户端代码使用Bearer token
3. 享受标准OpenAI兼容性的好处！

## 🎊 总结

现在你拥有了一个完全符合OpenAI标准的token认证系统：

- ✅ 10个预生成的OpenAI格式tokens
- ✅ 完整的token管理系统
- ✅ Bearer token认证
- ✅ 使用统计和跟踪
- ✅ CLI管理工具
- ✅ 管理API端点
- ✅ 完整的测试套件

你的API现在完全兼容OpenAI的认证标准！🚀
