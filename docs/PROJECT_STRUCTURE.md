# QJ Robots Python SDK - Project Structure

## 📁 Directory Organization

The project has been reorganized into a clean, modular structure for better maintainability and development experience.

```
py-qj-robots/
├── py_qj_robots/                    # 🏗️ Core SDK Package
│   ├── __init__.py                  # Package initialization
│   ├── perception.py                # Main perception API implementation
│   ├── authorization.py             # Authentication and token management
│   └── openai_api.py               # OpenAI compatibility layer
│
├── docs/                            # 📚 Documentation
│   ├── OPENAI_API_README.md         # OpenAI API comprehensive guide
│   ├── TOKEN_AUTHENTICATION_GUIDE.md # Token setup and management
│   ├── INSTALLATION_GUIDE.md        # Detailed installation instructions
│   ├── QUICK_START_GUIDE.md         # Quick start tutorial
│   ├── DEPENDENCIES_SUMMARY.md      # Dependencies overview
│   └── PROJECT_STRUCTURE.md         # This file
│
├── tests/                           # 🧪 Test Scripts
│   ├── __init__.py                  # Test package initialization
│   ├── test_openai_api.py          # OpenAI API compatibility tests
│   ├── test_token_auth.py          # Token authentication tests
│   ├── verify_installation.py      # Installation verification
│   ├── async_submit_performance_test.py # Performance tests
│   ├── concurrent_test.py          # Concurrency tests
│   ├── high_performance_test.py    # High performance tests
│   ├── performance_debug_test.py   # Performance debugging
│   ├── qps_validation_test.py      # QPS validation
│   ├── server_concurrency_test.py  # Server concurrency tests
│   └── stress_test.py              # Stress testing
│
├── examples/                        # 💡 Example Scripts
│   ├── __init__.py                  # Examples package initialization
│   ├── openai_api_example.py       # Direct OpenAI API usage examples
│   ├── openai_client_example.py    # HTTP client examples
│   └── async_usage_example.py      # Async usage patterns
│
├── utils/                           # 🛠️ Utility Scripts
│   ├── __init__.py                  # Utils package initialization
│   ├── token_config.py             # Token configuration and management
│   ├── manage_tokens.py            # CLI token management tool
│   └── install_dependencies.py     # Dependency installation script
│
├── openai_api_server.py            # 🚀 FastAPI Server
├── requirements.txt                 # 📦 Basic dependencies
├── requirements-openai.txt          # 📦 Full OpenAI API dependencies
├── setup.py                        # 📦 Package setup configuration
├── README.md                       # 📖 Main project documentation
├── LICENSE                         # 📄 License file
└── token_mapping.json              # 🔑 Token-to-credentials mapping
```

## 📂 Directory Details

### `py_qj_robots/` - Core SDK Package
The main Python package containing the core functionality:

- **`perception.py`**: Main perception API with all 7 perception functions
- **`authorization.py`**: Handles authentication and access token management
- **`openai_api.py`**: OpenAI compatibility layer with chat completion interface
- **`__init__.py`**: Exports main classes (`Perception`, `OpenAIPerceptionAPI`)

### `docs/` - Documentation
All documentation files organized by topic:

- **API Guides**: Comprehensive guides for different API usage patterns
- **Setup Guides**: Installation and configuration instructions
- **Reference**: Technical documentation and project structure

### `tests/` - Test Scripts
Comprehensive test suite covering all functionality:

- **Unit Tests**: `test_openai_api.py`, `test_token_auth.py`
- **Integration Tests**: `verify_installation.py`
- **Performance Tests**: Various performance and stress testing scripts
- **Validation Tests**: QPS validation and concurrency testing

### `examples/` - Example Scripts
Practical examples demonstrating different usage patterns:

- **Direct API Usage**: How to use the SDK directly
- **OpenAI Client Usage**: Using standard OpenAI client libraries
- **Async Patterns**: Asynchronous usage examples

### `utils/` - Utility Scripts
Administrative and management tools:

- **Token Management**: CLI tools for managing authentication tokens
- **Installation**: Automated dependency installation and verification
- **Configuration**: Token configuration and mapping utilities

## 🔧 Key Files

### Core Application Files
- **`openai_api_server.py`**: FastAPI server providing OpenAI-compatible REST API
- **`token_mapping.json`**: Configuration file mapping tokens to credentials

### Configuration Files
- **`requirements.txt`**: Basic dependencies for core SDK
- **`requirements-openai.txt`**: Complete dependencies including FastAPI server
- **`setup.py`**: Package configuration with optional dependencies

### Documentation
- **`README.md`**: Main project overview and quick start guide
- **`LICENSE`**: MIT license file

## 🚀 Usage Patterns

### Development Workflow
1. **Install Dependencies**: `python utils/install_dependencies.py`
2. **Configure Tokens**: `python utils/manage_tokens.py add APP_ID APP_SECRET`
3. **Run Tests**: `python tests/test_openai_api.py`
4. **Start Server**: `python openai_api_server.py`
5. **Try Examples**: `python examples/openai_api_example.py`

### Import Patterns
```python
# Core SDK
from py_qj_robots import Perception, OpenAIPerceptionAPI

# Utilities (when needed)
from utils.token_config import TokenManager

# Examples and tests import from parent directory
import sys, os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
```

## 📋 File Categories

### 🏗️ Core Implementation
- `py_qj_robots/` package
- `openai_api_server.py`

### 📚 Documentation
- All `.md` files in `docs/`
- `README.md`

### 🧪 Testing & Validation
- All files in `tests/`
- Performance and stress testing scripts

### 💡 Examples & Demos
- All files in `examples/`
- Usage demonstration scripts

### 🛠️ Tools & Utilities
- All files in `utils/`
- Management and configuration tools

### 📦 Configuration
- `requirements*.txt`
- `setup.py`
- `token_mapping.json`

## 🔄 Migration Notes

### Import Path Changes
After reorganization, some import paths have changed:

**Before:**
```python
from token_config import TokenManager
```

**After:**
```python
from utils.token_config import TokenManager
```

### File Locations
- Documentation: Root → `docs/`
- Tests: Root → `tests/`
- Utilities: Root → `utils/`
- Examples: Root → `examples/`

## 🎯 Benefits of New Structure

1. **🧹 Clean Organization**: Files grouped by purpose and functionality
2. **📚 Better Documentation**: All docs in one place with clear navigation
3. **🧪 Isolated Testing**: Test files separated from implementation
4. **💡 Clear Examples**: Examples grouped and easily discoverable
5. **🛠️ Utility Management**: Tools and utilities in dedicated directory
6. **📦 Package Structure**: Follows Python packaging best practices
7. **🔍 Easy Navigation**: Logical directory structure for development

## 🚀 Getting Started

With the new structure:

1. **Read Documentation**: Start with `README.md`, then explore `docs/`
2. **Install Dependencies**: Use `utils/install_dependencies.py`
3. **Run Tests**: Execute scripts in `tests/` directory
4. **Try Examples**: Run scripts in `examples/` directory
5. **Use Utilities**: Manage tokens with `utils/manage_tokens.py`

The reorganized structure makes the project more professional, maintainable, and easier to navigate for both development and usage! 🎉
