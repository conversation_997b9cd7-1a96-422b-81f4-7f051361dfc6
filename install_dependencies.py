#!/usr/bin/env python3
"""
Dependency installation script for QJ Robots OpenAI-compatible API.

This script helps install all required dependencies for the OpenAI API functionality.
"""

import subprocess
import sys
import os
from typing import List, <PERSON><PERSON>


def run_command(command: List[str]) -> Tuple[bool, str]:
    """Run a command and return success status and output"""
    try:
        result = subprocess.run(
            command, 
            capture_output=True, 
            text=True, 
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr
    except Exception as e:
        return False, str(e)


def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python 3.7+ is required for FastAPI")
        return False
    
    print("✅ Python version is compatible")
    return True


def install_package(package: str) -> bool:
    """Install a single package"""
    print(f"Installing {package}...")
    success, output = run_command([sys.executable, "-m", "pip", "install", package])
    
    if success:
        print(f"✅ {package} installed successfully")
        return True
    else:
        print(f"❌ Failed to install {package}: {output}")
        return False


def check_package_installed(package: str) -> bool:
    """Check if a package is already installed"""
    try:
        __import__(package.split('[')[0].split('>=')[0].split('==')[0])
        return True
    except ImportError:
        return False


def install_dependencies():
    """Install all required dependencies"""
    print("QJ Robots OpenAI API - Dependency Installation")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Core dependencies (always required)
    core_dependencies = [
        "requests>=2.26.0",
        "python-dotenv>=0.19.0"
    ]
    
    # OpenAI API dependencies (for server functionality)
    api_dependencies = [
        "fastapi>=0.68.0",
        "uvicorn[standard]>=0.15.0",
        "pydantic>=1.8.0"
    ]
    
    # Optional dependencies
    optional_dependencies = [
        "openai>=0.27.0"  # For client examples
    ]
    
    print("\n1. Installing core dependencies...")
    for package in core_dependencies:
        if not install_package(package):
            print(f"❌ Failed to install core dependency: {package}")
            return False
    
    print("\n2. Installing OpenAI API dependencies...")
    for package in api_dependencies:
        if not install_package(package):
            print(f"❌ Failed to install API dependency: {package}")
            return False
    
    print("\n3. Installing optional dependencies...")
    install_optional = input("Install OpenAI client library for examples? (y/n): ").lower().strip()
    
    if install_optional in ['y', 'yes']:
        for package in optional_dependencies:
            install_package(package)  # Don't fail if optional packages fail
    
    print("\n" + "=" * 50)
    print("✅ Dependency installation completed!")
    
    return True


def verify_installation():
    """Verify that all dependencies are properly installed"""
    print("\nVerifying installation...")
    
    # Test imports
    test_imports = [
        ("requests", "requests"),
        ("python-dotenv", "dotenv"),
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("pydantic", "pydantic")
    ]
    
    all_good = True
    
    for display_name, import_name in test_imports:
        try:
            __import__(import_name)
            print(f"✅ {display_name} - OK")
        except ImportError:
            print(f"❌ {display_name} - MISSING")
            all_good = False
    
    # Test optional imports
    try:
        import openai
        print("✅ openai (optional) - OK")
    except ImportError:
        print("⚠️  openai (optional) - Not installed (this is OK)")
    
    if all_good:
        print("\n🎉 All required dependencies are installed!")
        print("\nYou can now:")
        print("1. Run the server: python openai_api_server.py")
        print("2. Test the API: python test_openai_api.py")
        print("3. Try examples: python openai_api_example.py")
    else:
        print("\n❌ Some dependencies are missing. Please install them manually:")
        print("pip install fastapi uvicorn pydantic")
    
    return all_good


def create_requirements_dev():
    """Create a development requirements file with all dependencies"""
    dev_requirements = """# QJ Robots OpenAI API - Development Dependencies
# Install with: pip install -r requirements-dev.txt

# Core dependencies
requests>=2.26.0
python-dotenv>=0.19.0

# OpenAI API compatibility dependencies
fastapi>=0.68.0
uvicorn[standard]>=0.15.0
pydantic>=1.8.0

# Development and testing dependencies
pytest>=6.0.0
pytest-asyncio>=0.18.0
httpx>=0.23.0  # For testing FastAPI

# Optional dependencies for examples
openai>=0.27.0

# Code quality tools (optional)
black>=22.0.0
flake8>=4.0.0
mypy>=0.950
"""
    
    with open("requirements-dev.txt", "w") as f:
        f.write(dev_requirements)
    
    print("📝 Created requirements-dev.txt with all dependencies")


def main():
    """Main installation function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--verify-only":
        verify_installation()
        return
    
    if len(sys.argv) > 1 and sys.argv[1] == "--create-dev-requirements":
        create_requirements_dev()
        return
    
    print("This script will install dependencies for QJ Robots OpenAI API")
    print("Make sure you're in the correct virtual environment!")
    print()
    
    proceed = input("Continue with installation? (y/n): ").lower().strip()
    if proceed not in ['y', 'yes']:
        print("Installation cancelled.")
        return
    
    # Install dependencies
    if install_dependencies():
        # Verify installation
        verify_installation()
        
        # Create dev requirements
        create_dev_requirements()
        
        print("\n🚀 Setup complete! You're ready to use the OpenAI API.")
    else:
        print("\n❌ Installation failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
