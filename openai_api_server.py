#!/usr/bin/env python3
"""
FastAPI server providing OpenAI-compatible REST API for QJ Robots Perception.

This server exposes the perception capabilities through standard OpenAI API endpoints,
making it compatible with existing OpenAI client libraries and applications.

Usage:
    python openai_api_server.py

Then make requests to:
    POST http://localhost:8000/v1/chat/completions
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Union, Any
import json
import uvicorn
import logging
from datetime import datetime

from py_qj_robots.openai_api import OpenAIPerceptionAPI


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Pydantic models for request/response validation
class ChatMessage(BaseModel):
    role: str = Field(..., description="Role of the message sender")
    content: str = Field(..., description="Content of the message")
    name: Optional[str] = Field(None, description="Name of the sender")


class ChatCompletionRequest(BaseModel):
    model: str = Field(default="qj-perception-v1", description="Model to use")
    messages: List[ChatMessage] = Field(..., description="List of messages")
    functions: Optional[List[Dict]] = Field(None, description="Available functions")
    function_call: Optional[Union[str, Dict]] = Field(None, description="Function call behavior")
    temperature: Optional[float] = Field(0.7, description="Sampling temperature")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
    stream: Optional[bool] = Field(False, description="Whether to stream the response")
    top_p: Optional[float] = Field(1.0, description="Top-p sampling parameter")
    n: Optional[int] = Field(1, description="Number of completions to generate")
    stop: Optional[Union[str, List[str]]] = Field(None, description="Stop sequences")
    presence_penalty: Optional[float] = Field(0.0, description="Presence penalty")
    frequency_penalty: Optional[float] = Field(0.0, description="Frequency penalty")
    logit_bias: Optional[Dict[str, float]] = Field(None, description="Logit bias")
    user: Optional[str] = Field(None, description="User identifier")


class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str = "qj-robots"


class ModelsResponse(BaseModel):
    object: str = "list"
    data: List[ModelInfo]


# Initialize FastAPI app
app = FastAPI(
    title="QJ Robots Perception API",
    description="OpenAI-compatible API for QJ Robots Perception services",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize perception API
perception_api = OpenAIPerceptionAPI(enable_async_mode=True, max_workers=10)


@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "QJ Robots Perception API - OpenAI Compatible",
        "version": "1.0.0",
        "endpoints": {
            "chat_completions": "/v1/chat/completions",
            "models": "/v1/models",
            "functions": "/v1/functions",
            "docs": "/docs"
        }
    }


@app.get("/v1/models", response_model=ModelsResponse)
async def list_models():
    """List available models (OpenAI compatible)"""
    return ModelsResponse(
        data=[
            ModelInfo(
                id="qj-perception-v1",
                created=int(datetime.now().timestamp())
            )
        ]
    )


@app.get("/v1/functions")
async def list_functions():
    """List available perception functions"""
    functions = perception_api.list_functions()
    return {
        "object": "list",
        "data": functions
    }


@app.post("/v1/chat/completions")
async def create_chat_completion(request: ChatCompletionRequest):
    """Create a chat completion (OpenAI compatible)"""
    try:
        # Convert Pydantic models to dicts
        messages = [msg.dict() for msg in request.messages]
        
        # Create completion
        if request.stream:
            # Streaming response
            def generate_stream():
                try:
                    stream = perception_api.create_chat_completion(
                        messages=messages,
                        model=request.model,
                        functions=request.functions,
                        function_call=request.function_call,
                        temperature=request.temperature,
                        max_tokens=request.max_tokens,
                        stream=True
                    )
                    
                    for chunk in stream:
                        # Convert to dict and format as SSE
                        chunk_dict = {
                            "id": chunk.id,
                            "object": chunk.object,
                            "created": chunk.created,
                            "model": chunk.model,
                            "choices": chunk.choices
                        }
                        yield f"data: {json.dumps(chunk_dict)}\n\n"
                    
                    # Send final chunk
                    yield "data: [DONE]\n\n"
                    
                except Exception as e:
                    logger.error(f"Streaming error: {e}")
                    error_chunk = {
                        "error": {
                            "message": str(e),
                            "type": "perception_error"
                        }
                    }
                    yield f"data: {json.dumps(error_chunk)}\n\n"
            
            return StreamingResponse(
                generate_stream(),
                media_type="text/plain",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Content-Type": "text/event-stream"
                }
            )
        else:
            # Non-streaming response
            completion = perception_api.create_chat_completion(
                messages=messages,
                model=request.model,
                functions=request.functions,
                function_call=request.function_call,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                stream=False
            )
            
            # Convert to dict for JSON response
            return {
                "id": completion.id,
                "object": completion.object,
                "created": completion.created,
                "model": completion.model,
                "choices": [
                    {
                        "index": choice.index,
                        "message": {
                            "role": choice.message.role,
                            "content": choice.message.content,
                            "name": choice.message.name
                        },
                        "finish_reason": choice.finish_reason
                    }
                    for choice in completion.choices
                ],
                "usage": {
                    "prompt_tokens": completion.usage.prompt_tokens,
                    "completion_tokens": completion.usage.completion_tokens,
                    "total_tokens": completion.usage.total_tokens
                }
            }
            
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Internal error: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all requests"""
    start_time = datetime.now()
    
    # Log request
    logger.info(f"Request: {request.method} {request.url}")
    
    # Process request
    response = await call_next(request)
    
    # Log response
    process_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"Response: {response.status_code} ({process_time:.3f}s)")
    
    return response


if __name__ == "__main__":
    print("Starting QJ Robots Perception API Server...")
    print("OpenAI-compatible endpoints:")
    print("  POST /v1/chat/completions")
    print("  GET  /v1/models")
    print("  GET  /v1/functions")
    print("  GET  /docs (API documentation)")
    print("\nServer will be available at: http://localhost:8000")
    
    uvicorn.run(
        "openai_api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
