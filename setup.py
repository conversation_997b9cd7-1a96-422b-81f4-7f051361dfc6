from setuptools import setup, find_packages

setup(
    name="py-qj-robots",
    version="0.1.13",
    author="QJ ROBOTS",
    author_email="<EMAIL>",
    description="QJ Python SDK",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    url="https://github.com/QJ-ROBOTS/perception-python-sdk",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.0",
    install_requires=[
        "requests>=2.26.0",
        "python-dotenv>=0.19.0"
    ],
    extras_require={
        "openai": [
            "fastapi>=0.68.0",
            "uvicorn[standard]>=0.15.0",
            "pydantic>=1.8.0"
        ],
        "dev": [
            "fastapi>=0.68.0",
            "uvicorn[standard]>=0.15.0",
            "pydantic>=1.8.0",
            "pytest>=6.0.0",
            "pytest-asyncio>=0.18.0",
            "httpx>=0.23.0"
        ]
    },
)