#!/usr/bin/env python3
"""
Example client for the OpenAI-compatible QJ Robots Perception API.

This demonstrates how to use standard OpenAI client libraries to interact
with the perception API server, making it easy to integrate into existing
OpenAI-based applications.

Prerequisites:
    pip install openai requests

Usage:
    1. Start the server: python openai_api_server.py
    2. Run this client: python openai_client_example.py
"""

import json
import requests
import time
from typing import Dict, List


class PerceptionClient:
    """Client for the QJ Robots Perception API using OpenAI-compatible interface"""

    def __init__(self, base_url: str = "http://localhost:8000", api_key: str = None):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "User-Agent": "QJ-Robots-Perception-Client/1.0"
        })

        # Add authorization header if API key is provided
        if api_key:
            self.session.headers.update({
                "Authorization": f"Bearer {api_key}"
            })
    
    def list_models(self) -> Dict:
        """List available models"""
        response = self.session.get(f"{self.base_url}/v1/models")
        response.raise_for_status()
        return response.json()
    
    def list_functions(self) -> Dict:
        """List available perception functions"""
        response = self.session.get(f"{self.base_url}/v1/functions")
        response.raise_for_status()
        return response.json()
    
    def create_chat_completion(
        self,
        messages: List[Dict],
        model: str = "qj-perception-v1",
        stream: bool = False,
        **kwargs
    ) -> Dict:
        """Create a chat completion"""
        payload = {
            "model": model,
            "messages": messages,
            "stream": stream,
            **kwargs
        }
        
        if stream:
            return self._create_streaming_completion(payload)
        else:
            response = self.session.post(
                f"{self.base_url}/v1/chat/completions",
                json=payload
            )
            response.raise_for_status()
            return response.json()
    
    def _create_streaming_completion(self, payload: Dict):
        """Handle streaming completion"""
        response = self.session.post(
            f"{self.base_url}/v1/chat/completions",
            json=payload,
            stream=True
        )
        response.raise_for_status()
        
        chunks = []
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data = line[6:]  # Remove 'data: ' prefix
                    if data == '[DONE]':
                        break
                    try:
                        chunk = json.loads(data)
                        chunks.append(chunk)
                        yield chunk
                    except json.JSONDecodeError:
                        continue
    
    def health_check(self) -> Dict:
        """Check server health"""
        response = self.session.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()


def example_basic_perception():
    """Basic perception example"""
    print("=== Basic Perception Example ===")

    # Use one of the generated tokens
    api_key = "sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc"  # Replace with your token
    client = PerceptionClient(api_key=api_key)
    
    # Check if server is healthy
    try:
        health = client.health_check()
        print(f"Server status: {health['status']}")
    except Exception as e:
        print(f"Server not available: {e}")
        return
    
    # Object detection example
    messages = [
        {
            "role": "system",
            "content": "You are a helpful assistant that can analyze images."
        },
        {
            "role": "user",
            "content": json.dumps({
                "function": "check_image",
                "arguments": {
                    "image_type": "2D",
                    "image_url": "https://example.com/test-image.jpg",
                    "object_names": ["apple", "banana", "orange"]
                }
            })
        }
    ]
    
    try:
        response = client.create_chat_completion(messages=messages)
        print(f"Response ID: {response['id']}")
        print(f"Model: {response['model']}")
        print(f"Content: {response['choices'][0]['message']['content']}")
        print(f"Tokens used: {response['usage']['total_tokens']}")
    except Exception as e:
        print(f"Error: {e}")


def example_streaming_perception():
    """Streaming perception example"""
    print("\n=== Streaming Perception Example ===")
    
    client = PerceptionClient()
    
    messages = [
        {
            "role": "user",
            "content": json.dumps({
                "function": "split_image",
                "arguments": {
                    "image_type": "2D",
                    "image_url": "https://example.com/objects.jpg",
                    "object_names": ["cup", "plate", "spoon"]
                }
            })
        }
    ]
    
    try:
        print("Streaming response:")
        stream = client.create_chat_completion(messages=messages, stream=True)
        
        for chunk in stream:
            if 'choices' in chunk and chunk['choices']:
                delta = chunk['choices'][0].get('delta', {})
                if 'content' in delta:
                    print(delta['content'], end='', flush=True)
        print()  # New line after streaming
        
    except Exception as e:
        print(f"Error: {e}")


def example_different_functions():
    """Examples of different perception functions"""
    print("\n=== Different Function Examples ===")
    
    client = PerceptionClient()
    
    # List available functions first
    try:
        functions = client.list_functions()
        print("Available functions:")
        for func in functions['data']:
            func_info = func['function']
            print(f"  - {func_info['name']}: {func_info['description']}")
    except Exception as e:
        print(f"Error listing functions: {e}")
        return
    
    # Example function calls
    examples = [
        {
            "name": "Property Description",
            "function_call": {
                "function": "props_describe",
                "arguments": {
                    "image_type": "2D",
                    "image_url": "https://example.com/fruit.jpg",
                    "object_names": ["apple"],
                    "questions": ["What color is it?", "Is it fresh?"]
                }
            }
        },
        {
            "name": "Angle Prediction",
            "function_call": {
                "function": "angle_prediction",
                "arguments": {
                    "image_type": "2D",
                    "image_url": "https://example.com/tools.jpg",
                    "object_names": ["screwdriver"]
                }
            }
        },
        {
            "name": "Grasp Point Prediction",
            "function_call": {
                "function": "grab_point_prediction",
                "arguments": {
                    "image_type": "2D",
                    "image_url": "https://example.com/bottle.jpg",
                    "object_names": ["bottle"]
                }
            }
        }
    ]
    
    for example in examples:
        print(f"\n--- {example['name']} ---")
        messages = [
            {
                "role": "user",
                "content": json.dumps(example['function_call'])
            }
        ]
        
        try:
            response = client.create_chat_completion(messages=messages)
            content = response['choices'][0]['message']['content']
            print(f"Result: {content[:200]}{'...' if len(content) > 200 else ''}")
        except Exception as e:
            print(f"Error: {e}")


def example_with_openai_library():
    """Example using the official OpenAI library (if available)"""
    print("\n=== OpenAI Library Example ===")
    
    try:
        import openai
        
        # Configure OpenAI client to use our local server
        openai.api_base = "http://localhost:8000/v1"
        openai.api_key = "dummy-key"  # Not used but required by the library
        
        messages = [
            {
                "role": "user",
                "content": json.dumps({
                    "function": "check_image",
                    "arguments": {
                        "image_type": "2D",
                        "image_url": "https://example.com/scene.jpg",
                        "object_names": ["person", "car"]
                    }
                })
            }
        ]
        
        # Create completion using OpenAI library
        response = openai.ChatCompletion.create(
            model="qj-perception-v1",
            messages=messages
        )
        
        print("OpenAI library response:")
        print(f"Content: {response.choices[0].message.content}")
        
    except ImportError:
        print("OpenAI library not installed. Install with: pip install openai")
    except Exception as e:
        print(f"Error with OpenAI library: {e}")


def example_error_handling():
    """Error handling examples"""
    print("\n=== Error Handling Examples ===")
    
    client = PerceptionClient()
    
    # Test invalid function
    print("Testing invalid function...")
    messages = [
        {
            "role": "user",
            "content": json.dumps({
                "function": "invalid_function",
                "arguments": {}
            })
        }
    ]
    
    try:
        response = client.create_chat_completion(messages=messages)
        print("Unexpected success")
    except requests.exceptions.HTTPError as e:
        print(f"Expected error: {e.response.status_code} - {e.response.text}")
    
    # Test missing parameters
    print("\nTesting missing parameters...")
    messages = [
        {
            "role": "user",
            "content": json.dumps({
                "function": "check_image",
                "arguments": {
                    "image_type": "2D"
                    # Missing required parameters
                }
            })
        }
    ]
    
    try:
        response = client.create_chat_completion(messages=messages)
        print("Unexpected success")
    except requests.exceptions.HTTPError as e:
        print(f"Expected error: {e.response.status_code}")


def main():
    """Run all examples"""
    print("QJ Robots Perception API Client Examples")
    print("=" * 50)
    
    # Run examples
    example_basic_perception()
    example_streaming_perception()
    example_different_functions()
    example_with_openai_library()
    example_error_handling()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nNote: Examples may show errors if using fake image URLs.")
    print("Replace with real image URLs to test actual functionality.")


if __name__ == "__main__":
    main()
