#!/usr/bin/env python3
"""
Verify the new project structure and imports after reorganization.

This script checks that all files are in the correct locations and
that imports work properly after the directory restructuring.
"""

import os
import sys
from pathlib import Path


def check_directory_structure():
    """Check that all expected directories and files exist"""
    print("=== Checking Directory Structure ===")
    
    expected_structure = {
        "docs": [
            "OPENAI_API_README.md",
            "TOKEN_AUTHENTICATION_GUIDE.md", 
            "INSTALLATION_GUIDE.md",
            "QUICK_START_GUIDE.md",
            "DEPENDENCIES_SUMMARY.md",
            "PROJECT_STRUCTURE.md"
        ],
        "tests": [
            "__init__.py",
            "test_openai_api.py",
            "test_token_auth.py",
            "verify_installation.py"
        ],
        "examples": [
            "__init__.py",
            "openai_api_example.py",
            "openai_client_example.py",
            "async_usage_example.py"
        ],
        "utils": [
            "__init__.py",
            "token_config.py",
            "manage_tokens.py",
            "install_dependencies.py"
        ],
        "py_qj_robots": [
            "__init__.py",
            "perception.py",
            "authorization.py",
            "openai_api.py"
        ]
    }
    
    all_good = True
    
    for directory, files in expected_structure.items():
        if not os.path.exists(directory):
            print(f"❌ Directory missing: {directory}")
            all_good = False
            continue
        
        print(f"✅ Directory exists: {directory}")
        
        for file in files:
            file_path = os.path.join(directory, file)
            if os.path.exists(file_path):
                print(f"  ✅ {file}")
            else:
                print(f"  ❌ {file} - MISSING")
                all_good = False
    
    # Check root files
    root_files = [
        "README.md",
        "openai_api_server.py",
        "requirements.txt",
        "requirements-openai.txt",
        "setup.py"
    ]
    
    print(f"\n✅ Root directory files:")
    for file in root_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} - MISSING")
            all_good = False
    
    return all_good


def check_imports():
    """Check that imports work correctly after reorganization"""
    print("\n=== Checking Imports ===")
    
    all_good = True
    
    # Test core package imports
    try:
        from py_qj_robots import Perception, OpenAIPerceptionAPI
        print("✅ Core package imports work")
    except ImportError as e:
        print(f"❌ Core package import failed: {e}")
        all_good = False
    
    # Test utils imports
    try:
        from utils.token_config import TokenManager, TokenInfo
        print("✅ Utils imports work")
    except ImportError as e:
        print(f"❌ Utils import failed: {e}")
        all_good = False
    
    # Test server import (should work with updated path)
    try:
        # Add current directory to path for server import test
        sys.path.insert(0, os.getcwd())
        
        # This will test if the server can import its dependencies
        import importlib.util
        spec = importlib.util.spec_from_file_location("server", "openai_api_server.py")
        if spec and spec.loader:
            print("✅ Server import paths are correct")
        else:
            print("❌ Server import paths have issues")
            all_good = False
            
    except Exception as e:
        print(f"❌ Server import test failed: {e}")
        all_good = False
    
    return all_good


def check_file_contents():
    """Check that key files have correct import statements"""
    print("\n=== Checking File Contents ===")
    
    all_good = True
    
    # Check openai_api_server.py has correct import
    try:
        with open("openai_api_server.py", "r") as f:
            content = f.read()
            if "from utils.token_config import TokenManager" in content:
                print("✅ openai_api_server.py has correct import")
            else:
                print("❌ openai_api_server.py import needs updating")
                all_good = False
    except Exception as e:
        print(f"❌ Error checking openai_api_server.py: {e}")
        all_good = False
    
    # Check that __init__.py files exist and are not empty
    init_files = [
        "utils/__init__.py",
        "tests/__init__.py", 
        "examples/__init__.py"
    ]
    
    for init_file in init_files:
        if os.path.exists(init_file):
            with open(init_file, "r") as f:
                content = f.read().strip()
                if content:
                    print(f"✅ {init_file} exists and has content")
                else:
                    print(f"⚠️  {init_file} exists but is empty")
        else:
            print(f"❌ {init_file} missing")
            all_good = False
    
    return all_good


def show_new_structure():
    """Display the new project structure"""
    print("\n=== New Project Structure ===")
    
    def print_tree(directory, prefix="", max_depth=2, current_depth=0):
        if current_depth >= max_depth:
            return
            
        if not os.path.exists(directory):
            return
            
        items = sorted(os.listdir(directory))
        # Filter out hidden files and __pycache__
        items = [item for item in items if not item.startswith('.') and item != '__pycache__']
        
        for i, item in enumerate(items):
            path = os.path.join(directory, item)
            is_last = i == len(items) - 1
            
            current_prefix = "└── " if is_last else "├── "
            print(f"{prefix}{current_prefix}{item}")
            
            if os.path.isdir(path) and current_depth < max_depth - 1:
                extension = "    " if is_last else "│   "
                print_tree(path, prefix + extension, max_depth, current_depth + 1)
    
    print("py-qj-robots/")
    print_tree(".", max_depth=3)


def main():
    """Run all verification checks"""
    print("QJ Robots Project Structure Verification")
    print("=" * 50)
    
    checks = [
        ("Directory Structure", check_directory_structure),
        ("Import Statements", check_imports),
        ("File Contents", check_file_contents)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {check_name} check failed with error: {e}")
            results.append(False)
    
    # Show the new structure
    show_new_structure()
    
    # Summary
    print("\n" + "=" * 50)
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All {total} checks passed! Project structure is correct.")
        print("\nYou can now:")
        print("- Run tests: python tests/test_openai_api.py")
        print("- Start server: python openai_api_server.py")
        print("- Try examples: python examples/openai_api_example.py")
        print("- Manage tokens: python utils/manage_tokens.py list")
        return 0
    else:
        print(f"❌ {passed}/{total} checks passed. Please fix the issues above.")
        return 1


if __name__ == "__main__":
    exit(main())
