#!/usr/bin/env python3
"""
M1 Pro专用配置文件

基于MacBook Pro M1 Pro硬件特性优化的配置参数
"""

import os
import platform
from dataclasses import dataclass
from typing import Dict, List, Tuple


@dataclass
class M1ProHardwareSpec:
    """M1 Pro硬件规格"""
    model: str = "MacBook Pro M1 Pro"
    cpu_cores: int = 8
    performance_cores: int = 6
    efficiency_cores: int = 2
    memory_gb: int = 16
    network_bandwidth_mbps: int = 300
    architecture: str = "ARM64"


@dataclass
class M1ProOptimizedConfig:
    """M1 Pro优化配置"""
    # 线程配置
    max_workers_conservative: int
    max_workers_balanced: int
    max_workers_aggressive: int
    max_workers_extreme: int
    
    # 并发配置
    concurrency_levels: List[int]
    
    # 连接配置
    connection_pool_size: int
    max_connections: int
    
    # 超时配置
    timeout_fast: Tuple[int, int]
    timeout_normal: Tuple[int, int]
    timeout_safe: Tuple[int, int]
    
    # 测试配置
    requests_per_test: int
    batch_size: int
    
    # 性能配置
    thread_pool_multiplier: int
    max_thread_pool_size: int


class M1ProConfigGenerator:
    """M1 Pro配置生成器"""
    
    def __init__(self):
        self.hardware = M1ProHardwareSpec()
        self._validate_hardware()
    
    def _validate_hardware(self):
        """验证硬件环境"""
        if platform.machine() != 'arm64':
            print("⚠️  警告: 当前不是ARM64架构，配置可能不是最优")
        
        # 检测实际CPU核心数
        actual_cores = os.cpu_count()
        if actual_cores != self.hardware.cpu_cores:
            print(f"ℹ️  检测到CPU核心数: {actual_cores}，调整配置")
            self.hardware.cpu_cores = actual_cores
            # 假设性能核心占75%
            self.hardware.performance_cores = int(actual_cores * 0.75)
    
    def generate_conservative_config(self) -> M1ProOptimizedConfig:
        """生成保守配置 - 确保稳定性"""
        perf_cores = self.hardware.performance_cores
        
        return M1ProOptimizedConfig(
            # 线程配置 - 基于性能核心
            max_workers_conservative=perf_cores,
            max_workers_balanced=perf_cores * 2,
            max_workers_aggressive=perf_cores * 3,
            max_workers_extreme=perf_cores * 4,
            
            # 并发配置 - 保守
            concurrency_levels=[5, 10, 20, 30],
            
            # 连接配置
            connection_pool_size=30,
            max_connections=50,
            
            # 超时配置
            timeout_fast=(2, 8),
            timeout_normal=(3, 12),
            timeout_safe=(5, 20),
            
            # 测试配置
            requests_per_test=50,
            batch_size=10,
            
            # 性能配置
            thread_pool_multiplier=2,
            max_thread_pool_size=60
        )
    
    def generate_balanced_config(self) -> M1ProOptimizedConfig:
        """生成平衡配置 - 性能与稳定性平衡"""
        perf_cores = self.hardware.performance_cores
        
        return M1ProOptimizedConfig(
            # 线程配置
            max_workers_conservative=perf_cores * 2,
            max_workers_balanced=perf_cores * 4,
            max_workers_aggressive=perf_cores * 6,
            max_workers_extreme=perf_cores * 8,
            
            # 并发配置
            concurrency_levels=[20, 50, 100, 150],
            
            # 连接配置
            connection_pool_size=100,
            max_connections=150,
            
            # 超时配置
            timeout_fast=(1, 5),
            timeout_normal=(2, 8),
            timeout_safe=(3, 15),
            
            # 测试配置
            requests_per_test=100,
            batch_size=20,
            
            # 性能配置
            thread_pool_multiplier=3,
            max_thread_pool_size=150
        )
    
    def generate_aggressive_config(self) -> M1ProOptimizedConfig:
        """生成激进配置 - 追求最高性能"""
        perf_cores = self.hardware.performance_cores
        
        return M1ProOptimizedConfig(
            # 线程配置
            max_workers_conservative=perf_cores * 4,
            max_workers_balanced=perf_cores * 8,
            max_workers_aggressive=perf_cores * 12,
            max_workers_extreme=perf_cores * 16,
            
            # 并发配置
            concurrency_levels=[50, 100, 200, 300, 400],
            
            # 连接配置
            connection_pool_size=200,
            max_connections=300,
            
            # 超时配置
            timeout_fast=(1, 3),
            timeout_normal=(1, 5),
            timeout_safe=(2, 10),
            
            # 测试配置
            requests_per_test=200,
            batch_size=50,
            
            # 性能配置
            thread_pool_multiplier=4,
            max_thread_pool_size=300
        )
    
    def generate_extreme_config(self) -> M1ProOptimizedConfig:
        """生成极限配置 - 测试硬件极限"""
        perf_cores = self.hardware.performance_cores
        
        return M1ProOptimizedConfig(
            # 线程配置 - 极限
            max_workers_conservative=perf_cores * 8,
            max_workers_balanced=perf_cores * 12,
            max_workers_aggressive=perf_cores * 16,
            max_workers_extreme=perf_cores * 20,  # 120线程
            
            # 并发配置 - 极限
            concurrency_levels=[100, 200, 400, 600, 800, 1000],
            
            # 连接配置
            connection_pool_size=500,
            max_connections=800,
            
            # 超时配置 - 激进
            timeout_fast=(0.5, 2),
            timeout_normal=(1, 3),
            timeout_safe=(1, 5),
            
            # 测试配置
            requests_per_test=500,
            batch_size=100,
            
            # 性能配置
            thread_pool_multiplier=5,
            max_thread_pool_size=500
        )
    
    def get_recommended_config(self, performance_level: str = "balanced") -> M1ProOptimizedConfig:
        """获取推荐配置"""
        configs = {
            "conservative": self.generate_conservative_config,
            "balanced": self.generate_balanced_config,
            "aggressive": self.generate_aggressive_config,
            "extreme": self.generate_extreme_config
        }
        
        if performance_level not in configs:
            raise ValueError(f"不支持的性能级别: {performance_level}")
        
        return configs[performance_level]()
    
    def print_hardware_info(self):
        """打印硬件信息"""
        print(f"🖥️  硬件信息:")
        print(f"   型号: {self.hardware.model}")
        print(f"   CPU核心: {self.hardware.cpu_cores} (性能核心: {self.hardware.performance_cores})")
        print(f"   内存: {self.hardware.memory_gb}GB")
        print(f"   网络带宽: {self.hardware.network_bandwidth_mbps}Mbps")
        print(f"   架构: {self.hardware.architecture}")
    
    def print_config_comparison(self):
        """打印配置对比"""
        configs = {
            "保守": self.generate_conservative_config(),
            "平衡": self.generate_balanced_config(),
            "激进": self.generate_aggressive_config(),
            "极限": self.generate_extreme_config()
        }
        
        print(f"\n📊 M1 Pro配置对比:")
        print(f"{'配置级别':<8} {'最大线程':<8} {'并发范围':<15} {'请求数':<8} {'连接池':<8}")
        print("-" * 60)
        
        for name, config in configs.items():
            concurrency_range = f"{min(config.concurrency_levels)}-{max(config.concurrency_levels)}"
            print(f"{name:<8} {config.max_workers_extreme:<8} {concurrency_range:<15} "
                  f"{config.requests_per_test:<8} {config.connection_pool_size:<8}")


def get_m1_pro_config(performance_level: str = "balanced") -> M1ProOptimizedConfig:
    """获取M1 Pro优化配置的便捷函数"""
    generator = M1ProConfigGenerator()
    return generator.get_recommended_config(performance_level)


def main():
    """演示配置生成"""
    generator = M1ProConfigGenerator()
    
    # 显示硬件信息
    generator.print_hardware_info()
    
    # 显示配置对比
    generator.print_config_comparison()
    
    # 获取推荐配置
    config = generator.get_recommended_config("balanced")
    print(f"\n🎯 推荐配置 (平衡模式):")
    print(f"   最大线程数: {config.max_workers_extreme}")
    print(f"   并发级别: {config.concurrency_levels}")
    print(f"   每测试请求数: {config.requests_per_test}")
    print(f"   连接池大小: {config.connection_pool_size}")


if __name__ == "__main__":
    main()
